export default [
  {
    path: '/login',
    component: './Login',
    layout: false,
  },
  {
    path: '/',
    redirect: '/vehicle',
  },
  {
    path: '/vehicle',
    name: '车辆管理',
    component: './Vehicle',
    access: '010000',
    wrappers: ['@/wrappers/auth', '@/wrappers/globalData'],
  },
  {
    path: '/personnel',
    name: '人员管理',
    routes: [
      {
        path: 'employee',
        name: '员工信息管理',
        component: './Personnel/Employee',
        access: '020100',
      },
      {
        path: 'customer',
        name: '用户数据管理',
        component: './Personnel/Customer',
        access: '020200',
      },
      { path: '', redirect: 'employee' },
    ],
    wrappers: ['@/wrappers/auth', '@/wrappers/globalData'],
  },
  {
    path: '/appointment',
    name: '预约管理',
    component: './Appointment',
    access: '030000',
    wrappers: ['@/wrappers/auth', '@/wrappers/globalData'],
  },
  {
    path: '/analysis',
    name: '数据分析',
    routes: [
      { path: 'order', name: '订单数据', component: './Analysis/Order' },
      {
        path: 'promotionRecord',
        name: '推广数据',
        component: './Analysis/PromotionRecord',
      },
      {
        path: 'evaluation',
        name: '评价数据',
        component: './Analysis/Evaluation',
      },
      { path: 'income', name: '收入数据', component: './Analysis/Income' },
      { path: 'user', name: '用户数据', component: './Analysis/User' },
      {
        path: 'complaint',
        name: '投诉数据',
        component: './Analysis/Complaint',
      },
      { path: '', redirect: 'order' },
    ],
    access: '040000',
    wrappers: ['@/wrappers/auth', '@/wrappers/globalData'],
  },
  {
    path: '/miniprogram',
    name: '小程序模块管理',
    routes: [
      {
        path: 'banner',
        name: '轮播图',
        component: './Miniprogram/Banner',
        access: '050100',
      },
      {
        path: 'template',
        name: '内容模板',
        component: './Miniprogram/Template',
        access: '050200',
      },
      {
        path: 'photowall',
        name: '照片墙',
        component: './Miniprogram/PhotoWall',
        access: '050300',
      },
      {
        path: 'activity',
        name: '活动页',
        component: './Miniprogram/Activity',
        access: '050400',
      },
      { path: '', redirect: 'banner' },
    ],
    wrappers: ['@/wrappers/auth', '@/wrappers/globalData'],
  },
  {
    path: '/complaint',
    name: '投诉管理',
    component: './Complaint',
    wrappers: ['@/wrappers/auth', '@/wrappers/globalData'],
    access: '060000',
  },
  {
    path: '/attendance',
    name: '考勤管理',
    component: './Attendance',
    wrappers: ['@/wrappers/auth', '@/wrappers/globalData'],
    access: '070000',
  },
  {
    path: '/service',
    name: '服务管理',
    wrappers: ['@/wrappers/auth', '@/wrappers/globalData'],
    routes: [
      {
        path: 'serviceType',
        name: '服务品牌',
        component: './serviceManager/ServiceType',
        access: '080100',
      },
      {
        path: 'service',
        name: '服务项目',
        component: './serviceManager/Service',
        access: '080200',
      },
      { path: '', redirect: 'service' },
    ],
  },
  {
    path: '/coupon',
    name: '卡券管理',
    wrappers: ['@/wrappers/auth', '@/wrappers/globalData'],
    routes: [
      {
        path: 'rightsCard',
        name: '权益卡管理',
        component: './Coupon/RightsCard',
        access: '090100',
      },
      {
        path: 'coupon',
        name: '代金券管理',
        component: './Coupon/Coupon',
        access: '090200',
      },
      { path: '', redirect: 'rightsCard' },
    ],
  },
  {
    name: '基础数据管理',
    path: '/basicData',
    wrappers: ['@/wrappers/auth', '@/wrappers/globalData'],
    routes: [
      {
        name: '数据字典',
        path: 'dictionarie',
        component: './basicData/Dictionarie',
        access: '980100',
      },
      {
        name: '区域管理',
        path: 'area',
        component: './basicData/Area',
        access: '980200',
      },
      { path: '', redirect: 'dictionarie' },
    ],
  },
  {
    name: '系统管理',
    path: '/settings',
    wrappers: ['@/wrappers/auth', '@/wrappers/globalData'],
    routes: [
      {
        name: '用户管理',
        path: 'users',
        component: './settings/Users',
        access: '990100',
      },
      {
        name: '角色管理',
        path: 'roles',
        component: './settings/Roles',
        access: '990200',
      },
      {
        name: '权限点维护',
        path: 'permissions',
        component: './settings/Permissions',
        access: '990300',
      },
      {
        name: '系统功能维护',
        path: 'features',
        component: './settings/Features',
        access: '990400',
      },
      { path: '', redirect: 'users' },
    ],
  },
  {
    path: '*',
    redirect: '/',
  },
] as IBestAFSRoute;

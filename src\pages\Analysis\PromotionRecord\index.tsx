import { index } from '@/services/promotion-record';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { message } from 'antd';
import React, { useRef } from 'react';

const PromotionRecord: React.FC = () => {
  const actionRef = useRef<ActionType>();

  const columns: ProColumns<API.PromotionRecord, 'text'>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      hideInTable: true,
      hideInSearch: true,
    },
    {
      title: '分享人',
      dataIndex: ['sharer', 'nickname'],
      key: 'sharerOpenid',
    },
    {
      title: '分享人系统用户ID',
      dataIndex: 'sharerUserId',
      key: 'sharerUserId',
    },
    {
      title: '分享时间',
      dataIndex: 'shareTime',
      key: 'shareTime',
      valueType: 'dateTime',
    },
    {
      title: '注册人',
      dataIndex: ['registrant', 'nickname'],
      key: 'registrantOpenid',
    },
    {
      title: '注册人系统用户ID',
      dataIndex: 'registrantUserId',
      key: 'registrantUserId',
    },
    {
      title: '注册时间',
      dataIndex: 'registerTime',
      key: 'registerTime',
      valueType: 'dateTime',
    },
  ];

  return (
    <>
      <ProTable<API.PromotionRecord>
        actionRef={actionRef}
        rowKey="id"
        search={false}
        columns={columns}
        options={false}
        request={async (params) => {
          const { errCode, msg, data } = await index(params);
          if (errCode) {
            message.error(msg || '列表查询失败');
            return {
              data: [],
              total: 0,
            };
          }
          return {
            data: data?.list || [],
            total: data?.total || 0,
          };
        }}
        toolBarRender={() => []}
      />
    </>
  );
};

export default PromotionRecord;

import { request } from '@umijs/max';

/** 查询列表  GET /orders */
export async function index(params: Record<string, any>) {
  return request<API.ResType<{ total?: number; list?: API.Order[] }>>(
    '/orders',
    {
      method: 'GET',
      params,
    },
  );
}

/** 获取订单日志 /orders/:orderId/logs */
export async function logs(id: number) {
  return request<API.ResType<API.ServiceChangeLog[]>>(`/orders/${id}/logs`, {
    method: 'GET',
  });
}

/** 按ID查询  GET /orders/:id */
export async function show(id: number) {
  return request<API.ResType<API.Order>>(`/orders/${id}`, {
    method: 'GET',
  });
}

/** 派单 Post /orders/{orderId}/deliver */
export async function deliver(id: number, employeeId: number) {
  return request<API.ResType<API.Order>>(`/orders/${id}/deliver`, {
    method: 'POST',
    data: {
      employeeId,
    },
  });
}

/** 转单 Post /orders/{orderId}/transfer */
export async function transfer(id: number, employeeId: number) {
  return request<API.ResType<API.Order>>(`/orders/${id}/transfer`, {
    method: 'POST',
    data: {
      employeeId,
    },
  });
}

/** 开始服务 Post /orders/{orderId}/start */
export async function start(id: number, employeeId: number) {
  return request<API.ResType<API.Order>>(`/orders/${id}/start`, {
    method: 'POST',
    data: {
      employeeId,
    },
  });
}

/** 完成订单 Post /orders/{orderId}/complete */
export async function complete(id: number, employeeId: number) {
  return request<API.ResType<API.Order>>(`/orders/${id}/complete`, {
    method: 'POST',
    data: {
      employeeId,
    },
  });
}

/** 审核退款 Post /orders/:sn/auditRefund */
export async function auditRefund(
  sn: string,
  info: { result: boolean; reason?: string; money?: number },
) {
  return request<API.ResType<API.Order>>(`/orders/${sn}/auditRefund`, {
    method: 'POST',
    data: info,
  });
}

/** 退款 Post /wepay/refund/:sn */
export async function refund(sn: string) {
  return request<API.ResType<API.Order>>(`/wepay/refund/${sn}`, {
    method: 'POST',
  });
}

/** 取消订单 Podt /orders/{orderId}/cancel */
export async function cancel(id: number) {
  return request<API.ResType<API.Order>>(`/orders/${id}/cancel`, {
    method: 'POST',
  });
}

/** 删除  DELETE /orders/:id */
export async function remove(id: number) {
  return request<API.ResType<unknown>>(`/orders/${id}`, {
    method: 'DELETE',
  });
}

import { request } from '@umijs/max';

/** 查询列表  GET /promotion-record */
export async function index(params: Record<string, any>) {
  return request<API.ResType<{ total?: number; list?: API.PromotionRecord[] }>>(
    '/promotion-record',
    {
      method: 'GET',
      params,
    },
  );
}

/** 创建  POST /promotion-record */
export async function create(body: API.PromotionRecord) {
  return request<API.ResType<API.PromotionRecord>>('/promotion-record', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 按ID查询  GET /promotion-record/:id */
export async function show(id: number) {
  return request<API.ResType<API.PromotionRecord>>(`/promotion-record/${id}`, {
    method: 'GET',
  });
}

/** 修改  PUT /promotion-record/:id */
export async function update(id: number, body: Partial<API.PromotionRecord>) {
  return request<API.ResType<unknown>>(`/promotion-record/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 删除  DELETE /promotion-record/:id */
export async function remove(id: number) {
  return request<API.ResType<unknown>>(`/promotion-record/${id}`, {
    method: 'DELETE',
  });
}

/** 获取推广数据统计  GET /promotion-record/statistics */
export async function statistics() {
  return request<API.ResType<{
    totalRecords: number;
    todayRecords: number;
    thisWeekRecords: number;
    thisMonthRecords: number;
    avgDailyRecords: number;
    activeSharers: number;
  }>>('/promotion-record/statistics', {
    method: 'GET',
  });
}

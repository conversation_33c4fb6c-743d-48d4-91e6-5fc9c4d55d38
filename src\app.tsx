import { LogoutOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import {
  AntdConfig,
  history,
  request as httpRequest,
  RequestConfig,
  RuntimeAntdConfig,
  RunTimeLayoutConfig,
} from '@umijs/max';
import { Breadcrumb, Dropdown, message } from 'antd';
import { getCurrentUser } from './services/auth';
import {
  getRefreshToken,
  getToken,
  logout,
  refreshToken,
  removeTokens,
  setTokens,
} from './utils/auth';

// 声明 InitialState 类型
export type InitialState = API.User | undefined;

// 全局初始化数据配置，用于 Layout 用户信息和权限初始化
// 更多信息见文档：https://umijs.org/docs/api/runtime-config#getinitialstate
export async function getInitialState(): Promise<InitialState> {
  const token = getToken();
  // 如果有token，获取用户信息
  if (token) {
    try {
      const { data } = await getCurrentUser();
      return data;
    } catch (error) {
      removeTokens();
      history.push('/login');
    }
  }
  return undefined;
}

export const layout: RunTimeLayoutConfig = ({ initialState }) => {
  return {
    logo: '/logo.png',
    menu: {
      locale: false,
    },
    layout: 'side',
    pure: !initialState,
    actionsRender: () => null,
    // menuFooterRender: (props) => {
    //   return (
    //     <div
    //       style={{
    //         width: '100%',
    //         height: '32px',
    //         lineHeight: '32px',
    //         display: 'flex',
    //         justifyContent: 'center',
    //         alignItems: 'center',
    //         padding: '0 16px 0 24px',
    //         letterSpacing: '0.15px',
    //       }}
    //     >
    //       {props?.collapsed ? '' : 'Version: '}1.0.0
    //     </div>
    //   );
    // },
    childrenRender: (dom) => {
      return (
        <PageContainer
          header={{
            title: '',
            breadcrumbRender: (props, defaultDom) => {
              return (
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                  }}
                >
                  {defaultDom || (
                    <Breadcrumb
                      items={[
                        { title: (props as any)?.currentMenu?.name || ' ' },
                      ]}
                      style={{ paddingBlockStart: '18px' }}
                    />
                  )}
                  <Dropdown
                    menu={{
                      items: [
                        {
                          key: 'logout',
                          icon: <LogoutOutlined />,
                          label: '退出登录',
                          onClick: logout,
                        },
                      ],
                    }}
                  >
                    <div
                      style={{
                        paddingBlockStart: '18px',
                        cursor: 'pointer',
                        display: 'flex',
                        alignItems: 'center',
                      }}
                    >
                      <img
                        src={initialState?.avatar}
                        style={{
                          width: '24px',
                          height: '24px',
                          borderRadius: '50%',
                          marginRight: '6px',
                        }}
                      />
                      {initialState?.nickname} 欢迎
                    </div>
                  </Dropdown>
                </div>
              );
            },
          }}
          childrenContentStyle={{ paddingInline: 0 }}
        >
          <div
            style={{
              height: 'calc(100vh - 98px)',
              overflowY: 'auto',
              paddingInline: '40px',
            }}
          >
            {dom}
          </div>
        </PageContainer>
      );
    },
    // antd layout全局样式
    token: {
      sider: {
        colorTextMenuTitle: '#eee',
        colorMenuBackground: '#253650',
        colorTextMenu: '#ccc',
        colorTextMenuItemHover: '#fff',
        colorTextMenuActive: '#fff',
        colorTextMenuSelected: '#fff',
        colorTextSubMenuSelected: '#fff',
        colorTextCollapsedButton: '#2f83ff',
        colorBgMenuItemHover: '#2f83ff',
        colorBgMenuItemActive: '#2f83ff',
        colorBgMenuItemSelected: '#2f83ff',
      },
      //   pageContainer: {
      //     paddingInlinePageContainerContent: 0,
      //     paddingBlockPageContainerContent: 0,
      //     colorBgPageContainer: 'rgb(203 226 255 / 60%)',
      //   },
    },
  };
};

export const antd: RuntimeAntdConfig = (
  memo: AntdConfig & { [key: string]: any },
) => {
  // 按需加载
  memo.import = true;

  // 配置 antd 的 App 包裹组件
  memo.appConfig = {
    // dark: true,
    message: {
      // 配置 message 最大显示数，超过限制时，最早的消息会被自动关闭
      maxCount: 1,
    },
  };

  memo.theme ??= {
    components: {
      Segmented: {
        itemSelectedBg: '#2979ff',
        itemSelectedColor: '#fff',
      },
      Collapse: {
        headerBg: '#2979ff',
      },
    },
  };
  memo.theme.token = {
    colorPrimary: '#2979ff', // 主题色
  };

  return memo;
};

export const request: RequestConfig = {
  baseURL: '/api',
  timeout: 10000,
  headers: { 'X-Requested-With': 'XMLHttpRequest' },
  errorConfig: {
    errorHandler: async (error: any) => {
      const { response, config } = error;

      // 添加一个标记，用于识别是否是重试请求
      if (config._isRetry) {
        message.error(response?.data?.message || '请求失败');
        removeTokens();
        return Promise.reject(error);
      }

      if (response?.status === 401) {
        // 如果是登录或刷新token接口，直接返回错误
        if (
          config.url.includes('/auth/login') ||
          config.url.includes('/auth/refresh')
        ) {
          message.error(response?.data?.message || '登录失败');
          return Promise.reject(error);
        }

        // 其他接口尝试刷新token
        try {
          const newToken = await refreshToken();
          if (newToken) {
            // 重试失败的请求
            config.headers.Authorization = `Bearer ${newToken}`;
            // 标记这是一个重试请求
            config._isRetry = true;
            return httpRequest(config.url, {
              ...config,
              headers: config.headers,
            });
          }
        } catch (e) {
          // 刷新token失败，清除tokens并跳转登录
          removeTokens();
          message.error('登录已过期，请重新登录');
          history.push('/login');
          return Promise.reject(error);
        }
      } else {
        message.error(response?.data?.msg || '请求失败');
        return Promise.reject(error);
      }
    },
  },
  requestInterceptors: [
    (config: any) => {
      const token = getToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
  ],
  responseInterceptors: [
    (response) => {
      // 处理header中的新token
      const newToken = response.headers['new-token'];
      if (newToken) {
        setTokens(newToken, getRefreshToken()!);
      }
      return response;
    },
  ],
};
